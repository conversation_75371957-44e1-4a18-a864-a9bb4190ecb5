<template>
  <div class="route-planner">
    <!-- 地图容器 -->
    <div id="map-container" class="map-container"></div>

    <!-- 优化后的控制面板 -->
    <div class="planner-panel" :class="{ collapsed: isPanelCollapsed }">
      <div class="panel-header">
        <h2>北京邮电大学导航</h2>
        <div class="panel-toggle" @click="togglePanel">
          <span class="toggle-icon">▶</span>
        </div>
      </div>

      <div class="panel-content">
        <!-- 地点选择区域 -->
        <div class="location-selector">
          <div class="selector-title">选择地点：</div>

          <!-- 起点选择 -->
          <div class="location-item">
            <div class="location-label">起点：</div>
            <div class="location-input">
              <div class="autocomplete-container">
                <input
                  type="text"
                  v-model="startSearchText"
                  @input="filterStartLocations"
                  @focus="showStartSuggestions = true"
                  @blur="handleStartBlur"
                  placeholder="请输入起点"
                  class="location-input-field"
                />
                <div v-if="showStartSuggestions && filteredStartLocations.length > 0" class="suggestions-container">
                  <div
                    v-for="location in filteredStartLocations"
                    :key="'start-'+location.vertex_id"
                    class="suggestion-item"
                    @mousedown="selectStartLocation(location)"
                  >
                    {{ location.label }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 途径点选择 -->
          <div class="waypoints-container">
            <div class="waypoints-header">
              <div class="waypoints-title">途径点：</div>
              <button @click="addWaypoint" class="add-waypoint-btn">
                <span>+</span> 添加途径点
              </button>
            </div>

            <div v-if="waypoints.length === 0" class="no-waypoints-message">
              未添加途径点，可点击上方按钮添加
            </div>

            <div v-for="(waypoint, index) in waypoints" :key="'waypoint-'+index" class="waypoint-item">
              <div class="waypoint-index">{{ index + 1 }}</div>
              <div class="waypoint-input">
                <div class="autocomplete-container">
                  <input
                    type="text"
                    v-model="waypoint.searchText"
                    @input="() => filterWaypointLocations(index)"
                    @focus="waypoint.showSuggestions = true"
                    @blur="() => handleWaypointBlur(index)"
                    placeholder="请输入途径点"
                    class="location-input-field"
                  />
                  <div v-if="waypoint.showSuggestions && waypoint.filteredLocations.length > 0" class="suggestions-container">
                    <div
                      v-for="location in waypoint.filteredLocations"
                      :key="'waypoint-'+index+'-'+location.vertex_id"
                      class="suggestion-item"
                      @mousedown="() => selectWaypointLocation(index, location)"
                    >
                      {{ location.label }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="waypoint-actions">
                <button @click="() => removeWaypoint(index)" class="remove-waypoint-btn">
                  <span>×</span>
                </button>
              </div>
            </div>
          </div>

          <!-- 终点选择 -->
          <div class="location-item">
            <div class="location-label">终点：</div>
            <div class="location-input">
              <div class="autocomplete-container">
                <input
                  type="text"
                  v-model="endSearchText"
                  @input="filterEndLocations"
                  @focus="showEndSuggestions = true"
                  @blur="handleEndBlur"
                  placeholder="请输入终点"
                  class="location-input-field"
                />
                <div v-if="showEndSuggestions && filteredEndLocations.length > 0" class="suggestions-container">
                  <div
                    v-for="location in filteredEndLocations"
                    :key="'end-'+location.vertex_id"
                    class="suggestion-item"
                    @mousedown="selectEndLocation(location)"
                  >
                    {{ location.label }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="selector-note">
            或者直接在地图上点击选择位置
          </div>

          <!-- 路径规划策略选择 -->
          <div class="strategy-selector">
            <div class="strategy-title">规划策略：</div>
            <div class="strategy-options">
              <div
                class="strategy-option"
                :class="{ active: selectedStrategy === 0 }"
                @click="selectStrategy(0)"
              >
                <span class="strategy-icon">🚗</span>
                <span>最短距离</span>
              </div>
              <div
                class="strategy-option"
                :class="{ active: selectedStrategy === 1 }"
                @click="selectStrategy(1)"
              >
                <span class="strategy-icon">⏱️</span>
                <span>最短时间</span>
              </div>
              <div
                class="strategy-option"
                :class="{ active: selectedStrategy === 3 }"
                @click="selectStrategy(3)"
              >
                <span class="strategy-icon">🚀</span>
                <span>智能出行</span>
              </div>
            </div>
          </div>
        </div>

        <div class="marker-controls">
          <div class="marker-mode">
            <div class="mode-title">地图标记模式：</div>
            <div class="mode-options">
              <div
                class="mode-option"
                :class="{ active: currentMode === 'start' }"
                @click="setMode('start')"
              >
                起点
              </div>
              <div
                class="mode-option"
                :class="{ active: currentMode === 'waypoint' }"
                @click="setMode('waypoint')"
              >
                途径点
              </div>
              <div
                class="mode-option"
                :class="{ active: currentMode === 'end' }"
                @click="setMode('end')"
              >
                终点
              </div>
            </div>
          </div>
          <div class="marker-instructions">
            {{ getInstructions() }}
          </div>
        </div>

        <div class="marker-info">
          <div class="marker-item" v-if="startMarker">
            <div class="marker-label">起点：</div>
            <div class="marker-value">{{ getLocationDisplayName(startLocationName) || '地图选择的位置' }}</div>
          </div>

          <!-- 途径点信息 -->
          <div v-for="(waypoint, index) in visibleWaypoints" :key="'waypoint-info-'+index" class="marker-item">
            <div class="marker-label">途径点{{ getWaypointIndex(waypoint) + 1 }}：</div>
            <div class="marker-value">{{ getLocationDisplayName(waypoint.locationName) || '地图选择的位置' }}</div>
          </div>

          <div class="marker-item" v-if="endMarker">
            <div class="marker-label">终点：</div>
            <div class="marker-value">{{ getLocationDisplayName(endLocationName) || '地图选择的位置' }}</div>
          </div>
        </div>

        <!-- 交通方式选择 -->
        <div class="transport-mode">
          <div class="mode-title">交通方式：</div>
          <div class="transport-options">
            <div
              v-for="mode in transportModes"
              :key="mode.value"
              class="transport-option"
              :class="{
                active: selectedTransportMode === mode.value,
                disabled: selectedStrategy === 3 && mode.value !== 'driving'
              }"
              @click="selectTransportMode(mode.value)"
            >
              <span class="mode-icon">{{ mode.icon }}</span>
              <span>{{ mode.label }}</span>
            </div>
          </div>
        </div>

        <!-- 路线信息 -->
        <div class="route-info">
          <div class="route-stats">
            <div class="stat-item">
              <span class="stat-label">距离：</span>
              <span class="stat-value">{{ routeDistance }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">预计时间：</span>
              <span class="stat-value">{{ routeDuration }}</span>
            </div>
          </div>

          <!-- 路线详情 -->
          <div v-if="routeSteps.length > 0" class="route-steps">
            <div class="steps-title">路线详情：</div>
            <div class="steps-list">
              <div v-for="(step, index) in routeSteps" :key="index" class="step-item">
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-content">{{ step }}</div>
              </div>
            </div>
          </div>

          <div class="action-buttons">
            <button @click="calculateBackendRoute" class="action-btn calculate-btn backend-btn" :disabled="!canCalculateBackend">
              规划路线
            </button>
            <button @click="startNavigation" class="action-btn navigate-btn" :disabled="!canCalculate">
              开始导航
            </button>
            <button @click="clearMarkers" class="action-btn clear-btn">
              清除标记
            </button>
          </div>

          <!-- 调试信息 -->
          <div class="debug-info" v-if="showDebugInfo">
            <div class="debug-title">调试信息：</div>
            <div class="debug-item">
              <span class="debug-label">起点ID:</span>
              <span class="debug-value">{{ selectedStartId || '未选择' }}</span>
            </div>
            <div v-for="(waypoint, index) in waypoints" :key="'waypoint-debug-'+index" class="debug-item">
              <span class="debug-label">途径点{{ index + 1 }} ID:</span>
              <span class="debug-value">{{ waypoint.vertexId || '未选择' }}</span>
            </div>
            <div class="debug-item">
              <span class="debug-label">终点ID:</span>
              <span class="debug-value">{{ selectedEndId || '未选择' }}</span>
            </div>
            <div class="debug-item">
              <span class="debug-label">策略:</span>
              <span class="debug-value">{{ getStrategyName(selectedStrategy) }}</span>
            </div>
            <div class="debug-item">
              <span class="debug-label">系统规划按钮状态:</span>
              <span class="debug-value">{{ canCalculateBackend ? '启用' : '禁用' }}</span>
            </div>

            <!-- 智能出行模式和骑行模式颜色图例 -->
            <div v-if="selectedStrategy === 3 || selectedTransportMode === 'riding' || ((selectedStrategy === 0 || selectedStrategy === 1) && selectedTransportMode === 'driving')" class="color-legend">
              <div class="legend-title">{{
                selectedStrategy === 3 ? '智能出行' :
                selectedTransportMode === 'riding' ? '骑行模式' :
                '智能出行'
              }}路线颜色说明：</div>
              <div class="legend-items">
                <div class="legend-item">
                  <div class="legend-color green"></div>
                  <span>骑行畅通 (拥挤度 ≥ 0.9)</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color yellow"></div>
                  <span>骑行一般 (拥挤度 0.5-0.8)</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color red"></div>
                  <span>骑行拥挤 (拥挤度 &lt; 0.5)</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color blue"></div>
                  <span>步行路段</span>
                </div>
              </div>
              <div class="legend-note">
                实线：骑行路段 | 虚线：步行路段
              </div>
            </div>

            <button @click="showDebugInfo = false" class="debug-close">关闭</button>
          </div>

          <div class="debug-toggle" @click="showDebugInfo = !showDebugInfo">
            {{ showDebugInfo ? '隐藏调试' : '显示调试' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, computed } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import axios from 'axios'

// 创建地图实例和标记点的引用
const mapInstance = ref(null)
const startMarker = ref(null)
const endMarker = ref(null)
const polyline = ref(null)
const currentMode = ref('start') // 'start', 'waypoint' 或 'end'
const routeDistance = ref('0 米')
const straightDistance = ref('0 米')
const routeDuration = ref('0 分钟')
const routeSteps = ref([])
const selectedTransportMode = ref('driving')
const hasRoute = ref(false)

// 地点选择相关
const locations = ref([])
const selectedStartId = ref('')
const selectedEndId = ref('')
const startLocationName = ref('')
const endLocationName = ref('')
const isLoading = ref(false)
const backendRoutePath = ref([])

// 途径点相关
const waypoints = ref([])
const currentWaypointIndex = ref(0) // 当前选择的途径点索引

// 自动完成相关
const startSearchText = ref('')
const endSearchText = ref('')
const filteredStartLocations = ref([])
const filteredEndLocations = ref([])
const showStartSuggestions = ref(false)
const showEndSuggestions = ref(false)

// 路径规划策略
const selectedStrategy = ref(0) // 0: 最短距离, 1: 最短时间

// 调试相关
const showDebugInfo = ref(false)

// 面板控制
const isPanelCollapsed = ref(false)

// 计算属性：是否可以使用后端算法计算路线
const canCalculateBackend = computed(() => {
  return selectedStartId.value && selectedEndId.value
})

// 计算属性：可见的途径点（有标记的）
const visibleWaypoints = computed(() => {
  return waypoints.value.filter(waypoint => waypoint.marker)
})

// 获取途径点在原数组中的索引
const getWaypointIndex = (waypoint) => {
  return waypoints.value.findIndex(wp => wp === waypoint)
}

// 提取位置显示名称（只显示地点名称，不显示坐标信息）
const getLocationDisplayName = (fullLocationName) => {
  if (!fullLocationName) return ''

  // 如果包含坐标信息，提取地点名称部分
  const match = fullLocationName.match(/^([^(]+)/)
  if (match) {
    return match[1].trim()
  }

  return fullLocationName
}

// 添加途径点
const addWaypoint = () => {
  waypoints.value.push({
    marker: null,
    vertexId: '',
    locationName: '',
    searchText: '',
    showSuggestions: false,
    filteredLocations: []
  })

  // 自动切换到途径点模式
  currentMode.value = 'waypoint'
  currentWaypointIndex.value = waypoints.value.length - 1
}

// 移除途径点
const removeWaypoint = (index) => {
  // 如果有标记，先从地图上移除
  if (waypoints.value[index].marker) {
    mapInstance.value.remove(waypoints.value[index].marker)
  }

  // 从数组中移除
  waypoints.value.splice(index, 1)

  // 如果移除后没有途径点，切换到起点或终点模式
  if (waypoints.value.length === 0) {
    if (!startMarker.value) {
      currentMode.value = 'start'
    } else if (!endMarker.value) {
      currentMode.value = 'end'
    }
  } else if (currentWaypointIndex.value >= waypoints.value.length) {
    // 如果当前索引超出范围，重置为最后一个
    currentWaypointIndex.value = waypoints.value.length - 1
  }

  // 如果已有路线，清除
  if (polyline.value) {
    mapInstance.value.remove(polyline.value)
    polyline.value = null
    routeDistance.value = '0 米'
    routeSteps.value = []
    hasRoute.value = false
  }
}

// 过滤途径点位置
const filterWaypointLocations = (index) => {
  const waypoint = waypoints.value[index]
  if (!waypoint.searchText.trim()) {
    waypoint.filteredLocations = []
    return
  }

  const searchText = waypoint.searchText.toLowerCase().trim()
  waypoint.filteredLocations = locations.value.filter(location =>
    location.label && location.label.toLowerCase().includes(searchText)
  ).slice(0, 10) // 限制最多显示10个结果
}

// 选择途径点位置
const selectWaypointLocation = (index, location) => {
  const waypoint = waypoints.value[index]
  waypoint.vertexId = location.vertex_id
  waypoint.searchText = location.label
  waypoint.showSuggestions = false

  // 使用公式计算经纬度
  const coordinates = calculateCoordinates(location.x, location.y)

  // 在地图上标记位置
  const position = new window.AMap.LngLat(coordinates.lng, coordinates.lat)
  addWaypointMarker(position, index)

  // 显示经纬度信息
  console.log(`途径点${index+1}坐标: X=${location.x}, Y=${location.y}, 经度=${coordinates.lng.toFixed(14)}, 纬度=${coordinates.lat.toFixed(14)}`)
  waypoint.locationName = location.label
}

// 处理途径点输入框失焦事件
const handleWaypointBlur = (index) => {
  // 延迟关闭建议列表，以便点击建议项时能够触发点击事件
  setTimeout(() => {
    const waypoint = waypoints.value[index]
    waypoint.showSuggestions = false

    // 如果没有选择任何地点但有输入文本，尝试匹配最接近的地点
    if (!waypoint.vertexId && waypoint.searchText.trim()) {
      const matchedLocation = locations.value.find(loc =>
        loc.label && loc.label.toLowerCase() === waypoint.searchText.toLowerCase().trim()
      )

      if (matchedLocation) {
        selectWaypointLocation(index, matchedLocation)
      }
    }
  }, 200)
}

// 在地图上添加途径点标记
const addWaypointMarker = (position, index) => {
  try {
    // 如果已经有标记，先移除
    if (waypoints.value[index].marker) {
      mapInstance.value.remove(waypoints.value[index].marker)
    }

    // 创建标记
    const marker = new window.AMap.Marker({
      position: position,
      title: `途径点${index+1}`,
      label: {
        content: `<div class="waypoint-label">${index+1}</div>`,
        direction: 'center'
      }
    });

    // 添加到地图
    mapInstance.value.add(marker)

    // 保存标记引用
    waypoints.value[index].marker = marker

    // 如果是通过地图点击添加的标记，尝试查找最近的地点
    if (!waypoints.value[index].vertexId) {
      findNearestLocationForWaypoint(position, index)
    }

    // 如果已有连线，移除
    if (polyline.value) {
      mapInstance.value.remove(polyline.value)
      polyline.value = null
      routeDistance.value = '0 米'
      routeSteps.value = []
      hasRoute.value = false
    }
  } catch (error) {
    console.error('添加途径点标记错误:', error)
  }
}

// 查找途径点最近的地点
const findNearestLocationForWaypoint = (position, index) => {
  if (!locations.value || locations.value.length === 0) {
    console.warn('没有可用的地点数据')
    return
  }

  // 计算与所有地点的距离
  const locationsWithDistance = locations.value.map(location => {
    // 使用公式计算经纬度
    const coordinates = calculateCoordinates(location.x, location.y)
    const locLng = coordinates.lng
    const locLat = coordinates.lat

    // 计算距离
    const distance = calculateDistance(
      position.lng, position.lat,
      locLng, locLat
    )

    return {
      ...location,
      distance,
      lng: locLng,
      lat: locLat
    }
  })

  // 按距离排序
  locationsWithDistance.sort((a, b) => a.distance - b.distance)

  // 获取最近的地点
  const nearest = locationsWithDistance[0]

  if (nearest) {
    console.log(`找到最近的途径点${index+1}:`, nearest.label, '距离:', nearest.distance.toFixed(2), '米')
    console.log(`坐标: X=${nearest.x}, Y=${nearest.y}, 经度=${nearest.lng.toFixed(14)}, 纬度=${nearest.lat.toFixed(14)}`)

    // 设置ID和名称
    waypoints.value[index].vertexId = nearest.vertex_id
    waypoints.value[index].locationName = nearest.label
    waypoints.value[index].searchText = nearest.label
  }
}

// 交通方式选项
const transportModes = [
  { label: '不限', value: 'driving', icon: '🚗' },
  { label: '步行', value: 'walking', icon: '🚶' },
  { label: '骑行', value: 'riding', icon: '🚲' },
  // { label: '公共交通', value: 'transit', icon: '🚌' }
]

// 计算属性：是否可以计算距离
const canCalculate = computed(() => {
  return startMarker.value && endMarker.value
})

// 选择交通方式
const selectTransportMode = (mode) => {
  // 如果是智能出行策略，只允许选择"不限"（driving）
  if (selectedStrategy.value === 3 && mode !== 'driving') {
    return
  }

  selectedTransportMode.value = mode
  // 如果已经有起点和终点，自动重新计算路线
  if (canCalculate.value && hasRoute.value) {
    calculateRoute()
  }
}

// 设置当前标记模式
const setMode = (mode) => {
  currentMode.value = mode
}

// 获取当前操作指引
const getInstructions = () => {
  if (!startMarker.value && currentMode.value === 'start') {
    return '请在地图上点击选择起点'
  } else if (currentMode.value === 'waypoint') {
    return '请在地图上点击选择途径点'
  } else if (!endMarker.value && currentMode.value === 'end') {
    return '请在地图上点击选择终点'
  } else if (startMarker.value && endMarker.value) {
    return '点击"系统规划路线"查看路径'
  } else {
    if (currentMode.value === 'start') {
      return '请选择起点'
    } else if (currentMode.value === 'waypoint') {
      return '请选择途径点'
    } else {
      return '请选择终点'
    }
  }
}



// 切换面板展开/收起
const togglePanel = () => {
  isPanelCollapsed.value = !isPanelCollapsed.value
  const toggleIcon = document.querySelector('.toggle-icon')

  if (isPanelCollapsed.value) {
    toggleIcon.textContent = '◀'
  } else {
    toggleIcon.textContent = '▶'
  }
}

// 在地图上添加标记
const addMarker = (position, type) => {
  try {
    // 如果是途径点模式
    if (type === 'waypoint') {
      // 如果没有途径点，先添加一个
      if (waypoints.value.length === 0) {
        addWaypoint()
      }

      // 添加途径点标记
      addWaypointMarker(position, currentWaypointIndex.value)
      return
    }

    // 如果已经有标记，先移除
    if (type === 'start' && startMarker.value) {
      mapInstance.value.remove(startMarker.value)
    } else if (type === 'end' && endMarker.value) {
      mapInstance.value.remove(endMarker.value)
    }

    // 创建标记 - 使用最简单的方式创建标记
    const marker = new window.AMap.Marker({
      position: position,
      title: type === 'start' ? '起点' : '终点'
    });

    // 添加到地图
    mapInstance.value.add(marker)

    // 保存标记引用
    if (type === 'start') {
      startMarker.value = marker

      // 如果是通过地图点击添加的标记，尝试查找最近的地点作为起点ID
      if (!selectedStartId.value) {
        findNearestLocation(position, 'start')
      }

      // 直接显示位置信息（如果没有通过findNearestLocation设置）
      if (!startLocationName.value) {
        startLocationName.value = '地图选择的位置'
      }
    } else {
      endMarker.value = marker

      // 如果是通过地图点击添加的标记，尝试查找最近的地点作为终点ID
      if (!selectedEndId.value) {
        findNearestLocation(position, 'end')
      }

      // 直接显示位置信息（如果没有通过findNearestLocation设置）
      if (!endLocationName.value) {
        endLocationName.value = '地图选择的位置'
      }
    }

    // 自动切换模式
    if (type === 'start') {
      // 如果有途径点，切换到途径点模式
      if (waypoints.value.length > 0) {
        currentMode.value = 'waypoint'
      }
      // 否则如果没有终点，切换到终点模式
      else if (!endMarker.value) {
        currentMode.value = 'end'
      }
    } else if (type === 'end') {
      // 如果没有起点，切换到起点模式
      if (!startMarker.value) {
        currentMode.value = 'start'
      }
      // 否则如果有途径点，切换到途径点模式
      else if (waypoints.value.length > 0) {
        currentMode.value = 'waypoint'
      }
    }

    // 如果已有连线，移除
    if (polyline.value) {
      mapInstance.value.remove(polyline.value)
      polyline.value = null
      routeDistance.value = '0 米'
      straightDistance.value = '0 米'
      routeSteps.value = []
      hasRoute.value = false
    }
  } catch (error) {
    console.error('添加标记错误:', error)
  }
}

// 根据公式计算经纬度
const calculateCoordinates = (x, y) => {
  // 经度 = 116.35526546085191 + 0.00001026514696 * Y坐标
  // 纬度 = 39.95804755710804 + 0.00000923342117 * X坐标
  const longitude = 116.35526546085191 + 0.000010265146964114 * x
  const latitude = 39.95804755710804 + 0.0000091118507731447 * y

  return {
    lng: longitude,
    lat: latitude
  }
}

// 查找最近的地点
const findNearestLocation = (position, type) => {
  if (!locations.value || locations.value.length === 0) {
    console.warn('没有可用的地点数据')
    return
  }

  // 计算与所有地点的距离
  const locationsWithDistance = locations.value.map(location => {
    // 使用公式计算经纬度
    const coordinates = calculateCoordinates(location.x, location.y)
    const locLng = coordinates.lng
    const locLat = coordinates.lat

    // 计算距离
    const distance = calculateDistance(
      position.lng, position.lat,
      locLng, locLat
    )

    return {
      ...location,
      distance,
      lng: locLng,
      lat: locLat
    }
  })

  // 按距离排序
  locationsWithDistance.sort((a, b) => a.distance - b.distance)

  // 获取最近的地点
  const nearest = locationsWithDistance[0]

  if (nearest) {
    console.log(`找到最近的${type === 'start' ? '起点' : '终点'}:`, nearest.label, '距离:', nearest.distance.toFixed(2), '米')
    console.log(`坐标: X=${nearest.x}, Y=${nearest.y}, 经度=${nearest.lng.toFixed(14)}, 纬度=${nearest.lat.toFixed(14)}`)

    // 设置ID和名称
    if (type === 'start') {
      selectedStartId.value = nearest.vertex_id
      startLocationName.value = nearest.label
      startSearchText.value = nearest.label
    } else {
      selectedEndId.value = nearest.vertex_id
      endLocationName.value = nearest.label
      endSearchText.value = nearest.label
    }
  }
}

// 计算两点之间的距离（米）
const calculateDistance = (lng1, lat1, lng2, lat2) => {
  // 将经纬度转换为弧度
  const toRadians = (degree) => degree * Math.PI / 180
  const radLat1 = toRadians(lat1)
  const radLng1 = toRadians(lng1)
  const radLat2 = toRadians(lat2)
  const radLng2 = toRadians(lng2)

  // 地球半径（米）
  const R = 6371000

  // Haversine 公式计算球面距离
  const dLat = radLat2 - radLat1
  const dLng = radLng2 - radLng1
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(radLat1) * Math.cos(radLat2) *
            Math.sin(dLng/2) * Math.sin(dLng/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))

  return R * c
}

// 计算路线
const calculateRoute = () => {
  if (!startMarker.value || !endMarker.value) {
    alert('请先选择起点和终点')
    return
  }

  const startPos = startMarker.value.getPosition()
  const endPos = endMarker.value.getPosition()

  // 计算直线距离 - 使用简单的球面距离计算
  // 将经纬度转换为弧度
  const toRadians = (degree) => degree * Math.PI / 180
  const lat1 = toRadians(startPos.lat)
  const lon1 = toRadians(startPos.lng)
  const lat2 = toRadians(endPos.lat)
  const lon2 = toRadians(endPos.lng)

  // 地球半径（米）
  const R = 6371000

  // Haversine 公式计算球面距离
  const dLat = lat2 - lat1
  const dLon = lon2 - lon1
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1) * Math.cos(lat2) *
            Math.sin(dLon/2) * Math.sin(dLon/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  const distance = R * c

  straightDistance.value = distance < 1000 ? `${distance.toFixed(0)} 米` : `${(distance / 1000).toFixed(2)} 公里`

  // 清除现有路线
  if (polyline.value) {
    mapInstance.value.remove(polyline.value)
    polyline.value = null
  }

  try {
    // 显示加载中状态
    routeDistance.value = '计算中...'
    routeDuration.value = '计算中...'
    routeSteps.value = []

    // 简化版本 - 只使用驾车路线规划
    const driving = new window.AMap.Driving({
      policy: window.AMap.DrivingPolicy.LEAST_TIME
    })

    // 搜索路线
    driving.search(
      new window.AMap.LngLat(startPos.lng, startPos.lat),
      new window.AMap.LngLat(endPos.lng, endPos.lat),
      (status, result) => {
        if (status === 'complete' && result.routes && result.routes.length > 0) {
          const route = result.routes[0]

          // 更新距离和时间
          const distance = route.distance
          routeDistance.value = distance < 1000 ? `${distance} 米` : `${(distance / 1000).toFixed(2)} 公里`

          // 转换时间（秒）为更友好的格式
          const duration = route.time
          if (duration < 60) {
            routeDuration.value = `${duration} 秒`
          } else if (duration < 3600) {
            routeDuration.value = `${Math.floor(duration / 60)} 分钟`
          } else {
            const hours = Math.floor(duration / 3600)
            const minutes = Math.floor((duration % 3600) / 60)
            routeDuration.value = `${hours} 小时 ${minutes} 分钟`
          }

          // 提取路线步骤
          const steps = []
          if (route.steps && route.steps.length > 0) {
            route.steps.forEach(step => {
              if (step.instruction) {
                steps.push(step.instruction)
              }
            })
          }
          routeSteps.value = steps.length > 0 ? steps : ['按照地图上的路线行驶']

          // 绘制路线
          try {
            polyline.value = new window.AMap.Polyline({
              path: route.path,
              strokeColor: '#3498db',
              strokeWeight: 6,
              strokeOpacity: 0.8,
              strokeStyle: 'solid',
              lineJoin: 'round'
            })

            mapInstance.value.add(polyline.value)

            // 调整视图以包含整个路线
            mapInstance.value.setFitView([startMarker.value, endMarker.value, polyline.value])
          } catch (drawError) {
            console.error('绘制路线错误:', drawError)
            // 绘制简单直线作为备选
            drawStraightLine(startPos, endPos)
          }

          // 标记已有路线
          hasRoute.value = true
        } else {
          // 路线规划失败，显示直线距离
          routeDistance.value = straightDistance.value + ' (直线距离)'
          routeDuration.value = '无法估算'
          routeSteps.value = ['无法获取详细路线信息']

          // 绘制直线
          drawStraightLine(startPos, endPos)
          hasRoute.value = false
        }
      }
    )
  } catch (error) {
    console.error('路线规划错误:', error)
    routeDistance.value = straightDistance.value + ' (直线距离)'
    routeDuration.value = '无法估算'
    routeSteps.value = ['无法获取详细路线信息']

    // 绘制直线
    drawStraightLine(startPos, endPos)
    hasRoute.value = false
  }
}

// 绘制直线连接两点
const drawStraightLine = (startPos, endPos) => {
  try {
    // 检查是否是骑行模式
    const isRidingMode = selectedTransportMode.value === 'riding'

    // 在骑行模式下使用绿色，其他模式使用红色
    const lineColor = isRidingMode ? '#27ae60' : '#ff4d4f'

    polyline.value = new window.AMap.Polyline({
      path: [startPos, endPos],
      strokeColor: lineColor,
      strokeWeight: 4,
      strokeOpacity: 0.8,
      strokeStyle: 'dashed',
      lineJoin: 'round',
      showDir: true          // 显示方向箭头
    })

    mapInstance.value.add(polyline.value)
    mapInstance.value.setFitView([startMarker.value, endMarker.value])
  } catch (error) {
    console.error('绘制直线错误:', error)
  }
}

// 开始导航
const startNavigation = () => {
  if (!startMarker.value || !endMarker.value) {
    alert('请先选择起点和终点')
    return
  }

  try {
    const startPos = startMarker.value.getPosition()
    const endPos = endMarker.value.getPosition()

    // 根据选择的交通方式设置导航模式
    let navMode = 'car' // 默认驾车

    // 根据选择的交通方式设置导航模式
    if (selectedTransportMode.value === 'walking') {
      navMode = 'walk'
    } else if (selectedTransportMode.value === 'riding') {
      navMode = 'bike'
    }

    console.log(`使用${navMode}导航模式`)

    // 构建高德导航 URL
    const navigationUrl = `https://uri.amap.com/navigation?from=${startPos.lng},${startPos.lat},起点&to=${endPos.lng},${endPos.lat},终点&mode=${navMode}&callnative=1`

    // 打开导航链接
    window.open(navigationUrl, '_blank')
  } catch (error) {
    console.error('启动导航错误:', error)
    alert('启动导航失败，请稍后再试')
  }
}

// 清除所有标记
const clearMarkers = () => {
  if (startMarker.value) {
    mapInstance.value.remove(startMarker.value)
    startMarker.value = null
  }

  if (endMarker.value) {
    mapInstance.value.remove(endMarker.value)
    endMarker.value = null
  }

  // 清除所有途径点标记
  waypoints.value.forEach(waypoint => {
    if (waypoint.marker) {
      mapInstance.value.remove(waypoint.marker)
    }
  })
  waypoints.value = []
  currentWaypointIndex.value = 0

  if (polyline.value) {
    if (Array.isArray(polyline.value)) {
      // 如果是数组（多条线段），逐个移除
      polyline.value.forEach(line => {
        mapInstance.value.remove(line);
      });
    } else {
      // 如果是单个线段
      mapInstance.value.remove(polyline.value);
    }
    polyline.value = null
  }

  // 清除输入框和选择状态
  startSearchText.value = ''
  endSearchText.value = ''
  selectedStartId.value = ''
  selectedEndId.value = ''
  startLocationName.value = ''
  endLocationName.value = ''
  filteredStartLocations.value = []
  filteredEndLocations.value = []
  selectedStrategy.value = 0 // 重置为最短距离策略

  routeDistance.value = '0 米'
  straightDistance.value = '0 米'
  routeSteps.value = []
  currentMode.value = 'start'
  hasRoute.value = false
}

// 获取所有地点数据
const fetchLocations = async () => {
  try {
    isLoading.value = true
    const response = await axios.get('http://localhost:5000/api/path/vertices')
    if (response.status === 200) {
      locations.value = response.data
      console.log('获取到地点数据:', locations.value.length, '个地点')
    } else {
      console.error('获取地点数据失败:', response.statusText)
    }
  } catch (error) {
    console.error('获取地点数据错误:', error)
  } finally {
    isLoading.value = false
  }
}

// 过滤起点位置
const filterStartLocations = () => {
  if (!startSearchText.value.trim()) {
    filteredStartLocations.value = []
    return
  }

  const searchText = startSearchText.value.toLowerCase().trim()
  filteredStartLocations.value = locations.value.filter(location =>
    location.label && location.label.toLowerCase().includes(searchText)
  ).slice(0, 10) // 限制最多显示10个结果
}

// 过滤终点位置
const filterEndLocations = () => {
  if (!endSearchText.value.trim()) {
    filteredEndLocations.value = []
    return
  }

  const searchText = endSearchText.value.toLowerCase().trim()
  filteredEndLocations.value = locations.value.filter(location =>
    location.label && location.label.toLowerCase().includes(searchText)
  ).slice(0, 10) // 限制最多显示10个结果
}

// 选择起点
const selectStartLocation = (location) => {
  selectedStartId.value = location.vertex_id
  startSearchText.value = location.label
  showStartSuggestions.value = false

  // 使用公式计算经纬度
  const coordinates = calculateCoordinates(location.x, location.y)

  // 在地图上标记位置
  const position = new window.AMap.LngLat(coordinates.lng, coordinates.lat)
  addMarker(position, 'start')

  // 显示经纬度信息
  console.log(`起点坐标: X=${location.x}, Y=${location.y}, 经度=${coordinates.lng.toFixed(14)}, 纬度=${coordinates.lat.toFixed(14)}`)
  startLocationName.value = location.label
}

// 选择终点
const selectEndLocation = (location) => {
  selectedEndId.value = location.vertex_id
  endSearchText.value = location.label
  showEndSuggestions.value = false

  // 使用公式计算经纬度
  const coordinates = calculateCoordinates(location.x, location.y)

  // 在地图上标记位置
  const position = new window.AMap.LngLat(coordinates.lng, coordinates.lat)
  addMarker(position, 'end')

  // 显示经纬度信息
  console.log(`终点坐标: X=${location.x}, Y=${location.y}, 经度=${coordinates.lng.toFixed(14)}, 纬度=${coordinates.lat.toFixed(14)}`)
  endLocationName.value = location.label
}

// 处理起点输入框失焦事件
const handleStartBlur = () => {
  // 延迟关闭建议列表，以便点击建议项时能够触发点击事件
  setTimeout(() => {
    showStartSuggestions.value = false

    // 如果没有选择任何地点但有输入文本，尝试匹配最接近的地点
    if (!selectedStartId.value && startSearchText.value.trim()) {
      const matchedLocation = locations.value.find(loc =>
        loc.label && loc.label.toLowerCase() === startSearchText.value.toLowerCase().trim()
      )

      if (matchedLocation) {
        selectStartLocation(matchedLocation)
      }
    }
  }, 200)
}

// 处理终点输入框失焦事件
const handleEndBlur = () => {
  // 延迟关闭建议列表，以便点击建议项时能够触发点击事件
  setTimeout(() => {
    showEndSuggestions.value = false

    // 如果没有选择任何地点但有输入文本，尝试匹配最接近的地点
    if (!selectedEndId.value && endSearchText.value.trim()) {
      const matchedLocation = locations.value.find(loc =>
        loc.label && loc.label.toLowerCase() === endSearchText.value.toLowerCase().trim()
      )

      if (matchedLocation) {
        selectEndLocation(matchedLocation)
      }
    }
  }, 200)
}

// 获取策略名称
const getStrategyName = (strategy) => {
  switch (strategy) {
    case 0: return '最短距离'
    case 1: return '最短时间'
    case 3: return '智能出行'
    default: return '未知策略'
  }
}

// 选择路径规划策略
const selectStrategy = (strategy) => {
  selectedStrategy.value = strategy

  // 如果选择智能出行策略，自动设置交通方式为"不限"
  if (strategy === 3) {
    selectedTransportMode.value = 'driving'
  }

  console.log(`已选择策略: ${getStrategyName(strategy)}`)
}

// 使用后端算法计算路线
const calculateBackendRoute = async () => {
  console.log('开始计算后端路线...')
  console.log('起点ID:', selectedStartId.value)
  console.log('终点ID:', selectedEndId.value)
  console.log('策略:', selectedStrategy.value)
  console.log('交通方式:', selectedTransportMode.value)

  if (!selectedStartId.value || !selectedEndId.value) {
    alert('请先选择起点和终点')
    return
  }

  try {
    isLoading.value = true
    routeDistance.value = '计算中...'
    routeDuration.value = '计算中...'
    routeSteps.value = []

    // 获取有效的途径点ID
    const waypointIds = waypoints.value
      .filter(wp => wp.vertexId)
      .map(wp => parseInt(wp.vertexId))

    console.log('途径点IDs:', waypointIds)

    // 根据交通方式和策略选择实际策略
    // 0: 最短距离, 1: 最短时间, 2: 可骑行, 3: 智能出行, 4: 骑行最短距离, 5: 骑行最短时间
    let actualStrategy = selectedStrategy.value

    // 如果选择了骑行模式，根据用户选择的策略调整
    if (selectedTransportMode.value === 'riding') {
      if (selectedStrategy.value === 0) {
        actualStrategy = 4  // 骑行最短距离策略
        console.log('使用骑行最短距离策略')
      } else if (selectedStrategy.value === 1) {
        actualStrategy = 5  // 骑行最短时间策略
        console.log('使用骑行最短时间策略')
      } else {
        actualStrategy = 2  // 默认可骑行策略
        console.log('使用默认自行车路线规划策略')
      }
    }
    // 如果选择了智能出行策略，使用策略3
    else if (selectedStrategy.value === 3) {
      actualStrategy = 3
      console.log('使用智能出行策略')
    }
    // 如果选择了最短时间/最短距离策略且出行方式为"不限"，使用智能出行策略
    else if ((selectedStrategy.value === 0 || selectedStrategy.value === 1) && selectedTransportMode.value === 'driving') {
      actualStrategy = 3
      console.log(`用户选择${selectedStrategy.value === 0 ? '最短距离' : '最短时间'}策略且出行方式为"不限"，使用智能出行策略`)
    }

    let requestData
    let apiUrl = 'http://localhost:5000/api/path/plan'

    if (waypointIds.length > 0) {
      // 使用多目的地路径规划API
      requestData = {
        start_id: parseInt(selectedStartId.value),
        destinations: waypointIds.concat([parseInt(selectedEndId.value)]), // 将终点添加到途径点列表末尾
        strategy: actualStrategy,
        algorithm: 'simple' // 使用简单顺序路径算法，按照用户指定的顺序访问途径点
      }
    } else {
      // 使用单目的地路径规划API
      requestData = {
        start_id: parseInt(selectedStartId.value),
        end_id: parseInt(selectedEndId.value),
        strategy: actualStrategy
      }
    }

    console.log('发送请求数据:', requestData)

    // 调用后端路径规划API
    const response = await axios.post(apiUrl, requestData)

    if (response.status === 200 && response.data) {
      console.log('后端路径规划结果:', response.data)

      // 处理路径数据
      if (response.data.path && response.data.vertexes) {
        // 保存路径数据
        backendRoutePath.value = response.data.path

        // 更新距离信息
        const distance = response.data.total_distance || 0

        // 骑行模式或智能出行模式下，分别显示骑行和步行距离
        const shouldShowDetailedDistance = (selectedTransportMode.value === 'riding' || selectedStrategy.value === 3 ||
          ((selectedStrategy.value === 0 || selectedStrategy.value === 1) && selectedTransportMode.value === 'driving'))

        if (shouldShowDetailedDistance && response.data.path_details && response.data.path_details.length > 0) {
          // 计算可骑行和不可骑行的距离
          let rideableDistance = 0;
          let nonRideableDistance = 0;

          response.data.path_details.forEach(segment => {
            const segmentDistance = segment.distance || 0;
            if (segment.is_rideable) {
              rideableDistance += segmentDistance;
            } else {
              nonRideableDistance += segmentDistance;
            }
          });

          // 格式化距离显示
          const totalDistance = rideableDistance + nonRideableDistance;

          // 格式化总距离
          const formatDistance = (dist) => {
            return dist < 1000 ? `${Math.round(dist)}米` : `${(dist / 1000).toFixed(1)}公里`;
          };

          let distanceText = `总计: ${formatDistance(totalDistance)}`;

          // 添加骑行和步行距离详情，分行显示
          if (rideableDistance > 0 && nonRideableDistance > 0) {
            distanceText += `\n骑行: ${formatDistance(rideableDistance)}`;
            distanceText += `\n步行: ${formatDistance(nonRideableDistance)}`;
          } else if (rideableDistance > 0) {
            distanceText += `\n全程骑行`;
          } else if (nonRideableDistance > 0) {
            distanceText += `\n全程步行`;
          }

          routeDistance.value = distanceText;
        } else {
          // 其他模式下直接显示总距离
          const formatDistance = (dist) => {
            return dist < 1000 ? `${Math.round(dist)}米` : `${(dist / 1000).toFixed(1)}公里`;
          };
          routeDistance.value = formatDistance(distance);
        }

        // 根据交通方式估算时间
        let timeInMinutes = 0;

        // 骑行模式或智能出行模式下，需要考虑每段路径的可骑行性
        const shouldShowDetailedTime = (selectedTransportMode.value === 'riding' || selectedStrategy.value === 3 ||
          ((selectedStrategy.value === 0 || selectedStrategy.value === 1) && selectedTransportMode.value === 'driving'))

        if (shouldShowDetailedTime && response.data.path_details && response.data.path_details.length > 0) {
          // 使用上面已经计算好的可骑行和不可骑行距离
          const rideableDistance = response.data.path_details.reduce((sum, segment) =>
            sum + (segment.is_rideable ? (segment.distance || 0) : 0), 0);

          const nonRideableDistance = response.data.path_details.reduce((sum, segment) =>
            sum + (!segment.is_rideable ? (segment.distance || 0) : 0), 0);

          // 计算骑行部分的时间（分钟）- 考虑拥挤度
          let rideTime = 0;
          response.data.path_details.forEach(segment => {
            if (segment.is_rideable) {
              const segmentDistance = segment.distance || 0;
              const crowding = segment.crowding || 1.0;
              const rideSpeed = 12 * crowding; // 骑行速度 = 12公里/小时 * 拥挤度
              rideTime += (segmentDistance / 1000) / rideSpeed * 60;
            }
          });

          // 计算步行部分的时间（分钟）- 考虑拥挤度
          let walkTime = 0;
          response.data.path_details.forEach(segment => {
            if (!segment.is_rideable) {
              const segmentDistance = segment.distance || 0;
              const crowding = segment.crowding || 1.0;
              const walkSpeed = 4 * crowding; // 步行速度 = 4公里/小时 * 拥挤度
              walkTime += (segmentDistance / 1000) / walkSpeed * 60;
            }
          });

          // 总时间 = 骑行时间 + 步行时间
          timeInMinutes = Math.ceil(rideTime + walkTime);

          console.log(`总距离: ${rideableDistance + nonRideableDistance}米`);
          console.log(`可骑行距离: ${rideableDistance}米, 骑行时间: ${rideTime.toFixed(2)}分钟 (考虑拥挤度)`);
          console.log(`不可骑行距离: ${nonRideableDistance}米, 步行时间: ${walkTime.toFixed(2)}分钟 (考虑拥挤度)`);
          console.log('骑行模式/智能出行 - 修正后的总时间:', timeInMinutes, '分钟');

          // 保存详细时间信息用于显示
          window.detailedTimeInfo = {
            rideTime: Math.ceil(rideTime),
            walkTime: Math.ceil(walkTime),
            hasRiding: rideableDistance > 0,
            hasWalking: nonRideableDistance > 0
          };
        } else {
          // 其他模式下使用平均速度
          let averageSpeed = 4; // 默认步行速度 4km/h

          // 根据交通方式调整平均速度
          if (selectedTransportMode.value === 'riding') {
            averageSpeed = 12; // 自行车平均速度 12km/h
          } else if (selectedTransportMode.value === 'driving') {
            averageSpeed = 30; // 驾车平均速度 30km/h
          } else if (selectedTransportMode.value === 'walking') {
            // 步行模式 - 考虑拥挤度的平均值
            if (response.data.path_details && response.data.path_details.length > 0) {
              const avgCrowding = response.data.path_details.reduce((sum, segment) =>
                sum + (segment.crowding || 1.0), 0) / response.data.path_details.length;
              averageSpeed = 4 * avgCrowding;
            } else {
              averageSpeed = 4; // 没有详细信息时使用默认值
            }
          }

          // 计算时间（分钟）- 使用向上取整，确保时间估计不会偏小
          timeInMinutes = Math.ceil((distance / 1000) / averageSpeed * 60);
        }

        // 格式化时间显示
        const formatTime = (minutes) => {
          if (minutes < 60) {
            return `${minutes}分钟`;
          } else {
            const hours = Math.floor(minutes / 60);
            const mins = minutes % 60;
            return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`;
          }
        };

        let timeText = `总计: ${formatTime(timeInMinutes)}`;

        // 如果是骑行模式或智能出行模式，添加详细时间信息，分行显示
        const shouldShowDetailedTimeDisplay = (selectedTransportMode.value === 'riding' || selectedStrategy.value === 3 ||
          ((selectedStrategy.value === 0 || selectedStrategy.value === 1) && selectedTransportMode.value === 'driving'))

        if (shouldShowDetailedTimeDisplay && window.detailedTimeInfo) {
          const info = window.detailedTimeInfo;
          if (info.hasRiding && info.hasWalking) {
            timeText += `\n骑行: ${formatTime(info.rideTime)}`;
            timeText += `\n步行: ${formatTime(info.walkTime)}`;
          } else if (info.hasRiding) {
            timeText += `\n全程骑行`;
          } else if (info.hasWalking) {
            timeText += `\n全程步行`;
          }
        }

        routeDuration.value = timeText;

        // 生成路径步骤说明 - 简化版本，只显示地点名称
        const steps = []
        const vertices = response.data.vertexes

        // 添加起点
        const startVertex = vertices.find(v => v.vertex_id === backendRoutePath.value[0])
        if (startVertex) {
          steps.push(`从 ${startVertex.label || '起点'} 出发`)
        }

        // 添加中间路径点
        for (let i = 1; i < backendRoutePath.value.length; i++) {
          const vertex = vertices.find(v => v.vertex_id === backendRoutePath.value[i])
          if (vertex) {
            // 检查是否是途径点
            const isWaypoint = waypointIds.includes(vertex.vertex_id)
            const isEndpoint = vertex.vertex_id === parseInt(selectedEndId.value)

            if (isWaypoint) {
              steps.push(`途径 ${vertex.label || '途径点'}`)
            } else if (isEndpoint) {
              steps.push(`到达终点 ${vertex.label || '终点'}`)
            } else {
              steps.push(`前往 ${vertex.label || `地点${i}`}`)
            }
          }
        }

        // 如果没有步骤，添加一个默认步骤
        if (steps.length === 0) {
          steps.push('按照地图上的路线行驶')
        }

        routeSteps.value = steps

        // 在地图上绘制路径
        drawPathOnMap(vertices, backendRoutePath.value, response.data.path_details || [])

        hasRoute.value = true
      } else {
        routeSteps.value = ['无法获取详细路线信息']
        routeDistance.value = '无法计算'
        routeDuration.value = '无法估算'
        hasRoute.value = false
      }
    } else {
      console.error('路径规划失败:', response.statusText)
      routeSteps.value = ['路径规划失败，请稍后再试']
      routeDistance.value = '无法计算'
      routeDuration.value = '无法估算'
      hasRoute.value = false
    }
  } catch (error) {
    console.error('路径规划错误:', error)
    routeSteps.value = ['路径规划出错，请稍后再试']
    routeDistance.value = '无法计算'
    routeDuration.value = '无法估算'
    hasRoute.value = false
  } finally {
    isLoading.value = false
  }
}

// 根据拥挤度获取颜色
const getCrowdingColor = (crowding) => {
  if (crowding >= 0.9) {
    return '#27ae60'  // 绿色 - 拥挤度高，通行顺畅
  } else if (crowding >= 0.5) {
    return '#f39c12'  // 黄色 - 拥挤度中等
  } else {
    return '#e74c3c'  // 红色 - 拥挤度低，比较拥挤
  }
}

// 在地图上绘制路径
const drawPathOnMap = (vertices, path, pathDetails = []) => {
  console.log('绘制路径 - 顶点数:', vertices.length, '路径点数:', path.length, '路径详情数:', pathDetails.length)
  try {
    // 清除现有路线代码已移至下方

    // 构建路径点数组
    const pathPoints = []
    for (const vertexId of path) {
      const vertex = vertices.find(v => v.vertex_id === vertexId)
      if (vertex) {
        // 使用公式计算经纬度
        const coordinates = calculateCoordinates(vertex.x, vertex.y)
        pathPoints.push(new window.AMap.LngLat(coordinates.lng, coordinates.lat))
      }
    }

    if (pathPoints.length < 2) {
      console.error('路径点不足，无法绘制路线')
      return
    }

    // 清除现有路线
    if (polyline.value) {
      if (Array.isArray(polyline.value)) {
        // 如果是数组（多条线段），逐个移除
        polyline.value.forEach(line => {
          mapInstance.value.remove(line);
        });
      } else {
        // 如果是单个线段
        mapInstance.value.remove(polyline.value);
      }
      polyline.value = null;
    }

    // 检查是否是骑行模式或智能出行模式
    const isRidingMode = selectedTransportMode.value === 'riding'
    const isSmartTravelMode = selectedStrategy.value === 3 ||
      ((selectedStrategy.value === 0 || selectedStrategy.value === 1) && selectedTransportMode.value === 'driving')
    // 使用传入的pathDetails参数

    try {
      if ((isRidingMode || isSmartTravelMode) && pathDetails.length > 0 && pathDetails.length + 1 >= pathPoints.length) {
        // 骑行模式或智能出行模式下，使用不同颜色标识可骑行和不可骑行路段
        // 创建多个折线，每段路径使用不同颜色
        const polylines = []

        for (let i = 0; i < pathDetails.length; i++) {
          if (i + 1 >= pathPoints.length) continue

          const segmentPath = [pathPoints[i], pathPoints[i + 1]]
          const isRideable = pathDetails[i].is_rideable
          const crowding = pathDetails[i].crowding || 1.0

          // 确定线条颜色和样式
          let strokeColor, strokeStyle

          if (isSmartTravelMode || isRidingMode) {
            // 智能出行模式和骑行模式：骑行路段根据拥挤度确定颜色，步行路段统一使用蓝色
            if (isRideable) {
              strokeColor = getCrowdingColor(crowding)  // 骑行路段根据拥挤度变色
              strokeStyle = 'solid'  // 骑行实线
            } else {
              strokeColor = '#3498db'  // 步行路段统一使用蓝色
              strokeStyle = 'dashed'  // 步行虚线
            }
          } else {
            // 其他模式：使用默认颜色
            strokeColor = '#3498db'
            strokeStyle = 'solid'
          }

          // 创建折线 - 使用带箭头的样式
          const segmentLine = new window.AMap.Polyline({
            path: segmentPath,
            strokeColor: strokeColor,
            strokeWeight: 6,        // 线条宽度
            strokeOpacity: 0.8,     // 线条透明度
            strokeStyle: strokeStyle,
            lineJoin: 'round',      // 拐角样式
            showDir: true,          // 显示方向箭头
          })

          polylines.push(segmentLine)
          mapInstance.value.add(segmentLine)
        }

        // 保存所有折线的引用
        polyline.value = polylines
        console.log(`已绘制${isSmartTravelMode ? '智能出行' : isRidingMode ? '骑行' : '混合'}路线，共`, polylines.length, '段')
      } else {
        // 非骑行模式或没有详细路径信息，使用单一颜色
        polyline.value = new window.AMap.Polyline({
          path: pathPoints,
          strokeColor: '#3498db', // 线条颜色
          strokeWeight: 6,        // 线条宽度
          strokeOpacity: 0.8,     // 线条透明度
          strokeStyle: 'solid',   // 线条样式
          lineJoin: 'round',      // 拐角样式
          showDir: true           // 显示方向箭头
        })

        // 添加到地图
        mapInstance.value.add(polyline.value)
        console.log('已绘制普通路线，路径点数:', pathPoints.length)
      }
    } catch (error) {
      console.error('绘制路线错误:', error)
      // 出错时使用简单路线作为备选
      polyline.value = new window.AMap.Polyline({
        path: pathPoints,
        strokeColor: '#3498db',
        strokeWeight: 6,
        strokeOpacity: 0.8,
        strokeStyle: 'solid',
        lineJoin: 'round'
      })

      mapInstance.value.add(polyline.value)
      console.log('使用备选方式绘制路线')
    }

    // 调整视图以包含整个路线
    const overlays = [startMarker.value, endMarker.value];

    // 添加途径点标记
    waypoints.value.forEach(waypoint => {
      if (waypoint.marker) {
        overlays.push(waypoint.marker);
      }
    });

    // 添加路线
    if (Array.isArray(polyline.value)) {
      // 如果是数组（多条线段），逐个添加
      polyline.value.forEach(line => {
        overlays.push(line);
      });
    } else if (polyline.value) {
      // 如果是单个线段
      overlays.push(polyline.value);
    }

    // 调整视图
    mapInstance.value.setFitView(overlays)
  } catch (error) {
    console.error('绘制路径错误:', error)
  }
}

onMounted(() => {
  // 使用 setTimeout 确保 DOM 已经完全渲染
  setTimeout(async () => {
    try {
      console.log('开始加载高德地图 API...')

      // 加载高德地图 API 及所有必要的插件
      window.AMap = await AMapLoader.load({
        key: '9ac93278af733b48f3c31aacb870082f',
        version: '2.0',
        plugins: ['AMap.ToolBar', 'AMap.Scale']
      })

      // 分步加载其他插件，避免一次性加载过多插件导致错误
      await new Promise(resolve => setTimeout(resolve, 300))

      try {
        // 加载导航相关插件
        await window.AMap.plugin(['AMap.Driving'])
        await window.AMap.plugin(['AMap.Walking'])
        await window.AMap.plugin(['AMap.Riding'])
        await window.AMap.plugin(['AMap.Transfer'])
      } catch (pluginError) {
        console.error('加载导航插件失败:', pluginError)
      }

      console.log('高德地图 API 加载成功')

      // 检查地图容器是否存在
      const mapContainer = document.getElementById('map-container')
      if (!mapContainer) {
        console.error('找不到地图容器元素')
        return
      }

      // 创建地图实例并保存引用
      mapInstance.value = new window.AMap.Map('map-container', {
        center: [116.3588, 39.9615], // 北京邮电大学坐标
        zoom: 17,
        viewMode: '2D',
        mapStyle: 'amap://styles/normal'
      })

      // 添加控件
      mapInstance.value.addControl(new window.AMap.ToolBar())
      mapInstance.value.addControl(new window.AMap.Scale())

      console.log('地图实例创建成功')

      // 添加点击事件监听器
      mapInstance.value.on('click', (e) => {
        // 根据当前模式添加标记
        addMarker(e.lnglat, currentMode.value)
      })

      // 获取所有地点数据
      await fetchLocations()

      console.log('地图点击事件监听器和地点数据加载成功')
    } catch (error) {
      console.error('地图加载失败:', error)
    }
  }, 500) // 延迟 500ms 确保 DOM 已渲染
})

onBeforeUnmount(() => {
  // 正确销毁地图实例
  if (mapInstance.value) {
    mapInstance.value.destroy()
  }
})
</script>

<style scoped>
.route-planner {
  position: relative;
  width: 100%;
  height: calc(100vh - 20px); /* 为上方导航区域留出间隔 */
  margin-top: 20px; /* 与上方导航区域的间隔 */
  display: flex;
}

.map-container {
  flex: 1;
  height: 100%;
  background-color: #f0f0f0;
}

#map-container {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.planner-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 380px;
  max-height: calc(100vh - 80px);
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transition: transform 0.3s ease;
  overflow: hidden;
}

.planner-panel.collapsed {
  transform: translateX(340px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.panel-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.panel-toggle {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  transition: background 0.2s;
}

.panel-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
}

.toggle-icon {
  font-size: 14px;
  font-weight: bold;
}

.panel-content {
  max-height: calc(100vh - 160px);
  overflow-y: auto;
  padding: 20px;
}

/* 地点选择区域样式 */
.location-selector {
  margin-bottom: 20px;
}

.selector-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.location-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.location-label {
  min-width: 50px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.location-input {
  flex: 1;
}

.location-input-field {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.location-input-field:focus {
  outline: none;
  border-color: #667eea;
}

/* 标记控制样式 */
.marker-controls {
  margin: 20px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.marker-mode {
  margin-bottom: 12px;
}

.mode-title {
  font-weight: 500;
  color: #555;
  font-size: 14px;
  margin-bottom: 8px;
}

.mode-options {
  display: flex;
  gap: 8px;
}

.mode-option {
  flex: 1;
  padding: 8px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  font-size: 13px;
}

.mode-option:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.mode-option.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.marker-instructions {
  font-size: 13px;
  color: #6c757d;
  text-align: center;
  font-style: italic;
}

.autocomplete-container {
  position: relative;
}

.suggestions-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e1e5e9;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1001;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item {
  padding: 10px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  transition: background-color 0.2s;
}

.suggestion-item:hover {
  background-color: #f8f9fa;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.selector-note {
  font-size: 12px;
  color: #6c757d;
  text-align: center;
  margin: 12px 0;
  font-style: italic;
}

/* 途径点样式 */
.waypoints-container {
  margin: 16px 0;
}

.waypoints-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.waypoints-title {
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.add-waypoint-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.add-waypoint-btn:hover {
  background: #218838;
}

.no-waypoints-message {
  color: #6c757d;
  font-size: 13px;
  font-style: italic;
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.waypoint-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.waypoint-index {
  min-width: 24px;
  height: 24px;
  background: #0066ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.waypoint-input {
  flex: 1;
}

.waypoint-actions {
  display: flex;
  gap: 4px;
}

.remove-waypoint-btn {
  background: #dc3545;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.remove-waypoint-btn:hover {
  background: #c82333;
}

.waypoint-label {
  background: #722ed1;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* 策略选择样式 */
.strategy-selector {
  margin: 16px 0;
}

.strategy-title {
  font-weight: 500;
  color: #555;
  font-size: 14px;
  margin-bottom: 8px;
}

.strategy-options {
  display: flex;
  gap: 8px;
}

.strategy-option {
  flex: 1;
  min-width: 100px;
  padding: 10px 8px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  font-size: 13px;
}

.strategy-option:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.strategy-option.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.strategy-icon {
  display: block;
  font-size: 16px;
  margin-bottom: 4px;
}

/* 响应式设计 - 小屏幕适配 */
@media (max-width: 400px) {
  .strategy-options {
    gap: 4px;
  }

  .strategy-option {
    min-width: 60px;
    max-width: 90px;
    padding: 6px 3px;
  }

  .strategy-option .strategy-icon {
    font-size: 1rem;
    margin-bottom: 2px;
  }

  .strategy-option span:not(.strategy-icon) {
    font-size: 0.75rem;
  }
}

/* 标记信息样式 */
.marker-info {
  margin: 16px 0;
  padding: 12px;
  background: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.marker-item {
  display: flex;
  margin-bottom: 6px;
  font-size: 13px;
}

.marker-item:last-child {
  margin-bottom: 0;
}

.marker-label {
  min-width: 60px;
  font-weight: 500;
  color: #555;
}

.marker-value {
  flex: 1;
  color: #333;
}

/* 交通方式样式 */
.transport-mode {
  margin: 16px 0;
}

.transport-options {
  display: flex;
  gap: 8px;
}

.transport-option {
  flex: 1;
  min-width: 80px;
  padding: 10px 8px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  font-size: 13px;
}

.transport-option:hover:not(.disabled) {
  border-color: #667eea;
  background: #f8f9ff;
}

.transport-option.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.transport-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f5f5f5;
}

.mode-icon {
  display: block;
  font-size: 16px;
  margin-bottom: 4px;
}

/* 路线信息样式 */
.route-info {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
}

.route-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  flex: 1;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 路线步骤样式 */
.route-steps {
  margin: 16px 0;
}

.steps-title {
  font-weight: 500;
  color: #555;
  font-size: 14px;
  margin-bottom: 8px;
}

.steps-list {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  font-size: 13px;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  min-width: 20px;
  height: 20px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: bold;
  margin-right: 8px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  line-height: 1.4;
  color: #333;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  margin: 16px 0;
}

.action-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.calculate-btn {
  background: #28a745;
  color: white;
}

.calculate-btn:hover:not(:disabled) {
  background: #218838;
}

.navigate-btn {
  background: #007bff;
  color: white;
}

.navigate-btn:hover:not(:disabled) {
  background: #0056b3;
}

.clear-btn {
  background: #6c757d;
  color: white;
}

.clear-btn:hover {
  background: #545b62;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 调试信息样式 */
.debug-info {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  position: relative;
}

.debug-title {
  font-weight: 500;
  margin-bottom: 10px;
  color: #555;
}

.debug-item {
  margin-bottom: 8px;
  display: flex;
}

.debug-label {
  font-weight: 500;
  color: #666;
  width: 120px;
}

.debug-value {
  color: #333;
}

.debug-close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 0.8rem;
}

.debug-toggle {
  margin-top: 10px;
  text-align: center;
  font-size: 0.8rem;
  color: #999;
  cursor: pointer;
  text-decoration: underline;
}

.debug-toggle:hover {
  color: #666;
}



/* 信息窗口 */
.info-window {
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.15);
  max-width: 250px;
}

.info-window h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1rem;
}

.info-window p {
  margin: 5px 0;
  color: #666;
  font-size: 0.9rem;
}

/* 路线动画 */
.polyline-animate {
  animation: pathDraw 1.5s ease-out forwards;
}

@keyframes pathDraw {
  from {
    stroke-dashoffset: 1000;
    opacity: 0;
  }
  to {
    stroke-dashoffset: 0;
    opacity: 1;
  }
}

/* 颜色图例样式 */
.color-legend {
  margin: 15px 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.legend-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: #555;
}

.legend-color {
  width: 20px;
  height: 3px;
  border-radius: 2px;
}

.legend-color.green {
  background-color: #27ae60;
}

.legend-color.yellow {
  background-color: #f39c12;
}

.legend-color.red {
  background-color: #e74c3c;
}

.legend-color.blue {
  background-color: #3498db;
}

.legend-note {
  font-size: 0.75rem;
  color: #666;
  text-align: center;
  font-style: italic;
  border-top: 1px solid #e9ecef;
  padding-top: 6px;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar,
.steps-list::-webkit-scrollbar,
.suggestions-container::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track,
.steps-list::-webkit-scrollbar-track,
.suggestions-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb,
.steps-list::-webkit-scrollbar-thumb,
.suggestions-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover,
.steps-list::-webkit-scrollbar-thumb:hover,
.suggestions-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .planner-panel {
    width: calc(100% - 40px);
    max-width: 400px;
  }

  .strategy-options {
    flex-direction: column;
  }

  .strategy-option {
    min-width: auto;
  }

  .transport-options {
    flex-direction: column;
  }

  .transport-option {
    min-width: auto;
  }

  .route-stats {
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons {
    flex-direction: column;
  }
}
</style>
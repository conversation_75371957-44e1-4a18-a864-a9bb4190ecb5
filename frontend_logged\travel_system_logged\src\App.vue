/* eslint-disable */
<template>
  <div id="app">
    <!-- 导航栏只在非登录页面显示 -->
    <el-header class="global-header" :class="{ 'scrolled': isScrolled }" v-if="!isLoginPage">
      <div class="header-container">
        <!-- 添加点击事件的logo区域 -->
        <div class="header-title" @click="goToHome">
          <img
            src="@/assets/New_Logo.png"
            alt="系统图标"
            class="header-logo"
            @click.stop="goToHome"
          >
          <!-- 移除未定义的 TravelIcon 组件 -->
          <span class="header-text">鸿雁智游</span>
        </div>

        <!-- 导航菜单 -->
        <el-menu
          mode="horizontal"
          :default-active="activeMenu"
          @select="handleMenuSelect"
          class="custom-menu"
          background-color="transparent"
          text-color="#ffffff"
          active-text-color="#ffffff"
        >
          <el-menu-item index="recommend" class="menu-item">
            <el-icon><Star /></el-icon>
            <span>景点推荐</span>
          </el-menu-item>
          <el-menu-item index="route" class="menu-item">
            <el-icon><Location /></el-icon>
            <span>路线规划</span>
          </el-menu-item>
          <el-menu-item index="search" class="menu-item">
            <el-icon><Position /></el-icon>
            <span>场所查询</span>
          </el-menu-item>
          <el-menu-item index="flight" class="menu-item">
            <el-icon><Ticket /></el-icon>
            <span>机票预订</span>
          </el-menu-item>
          <el-menu-item index="food" class="menu-item">
            <el-icon><KnifeFork /></el-icon>
            <span>美食推荐</span>
          </el-menu-item>
          <el-menu-item index="diary" class="menu-item">
            <el-icon><Notebook /></el-icon>
            <span>游记社区</span>
          </el-menu-item>
          <el-menu-item index="ai-generator" class="menu-item ai-menu-item">
            <el-icon><MagicStick /></el-icon>
            <span>AI生成</span>
          </el-menu-item>
        </el-menu>

        <div class="header-right">
           <div class="avatar-container" @click="handleAvatarClick">
                <img
                    :src="avatarUrl"
                    alt="用户头像"
                    class="right-image"
                >
            </div>
            <div class="avatar-text" @click="handleTextClick">{{ avatarText }}</div>
        </div>
      </div>
    </el-header>

    <LoginDialog ref="loginDialogRef" />

    <el-dialog
      v-model="avatarDialogVisible"
      title="更换头像"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="avatar-upload-container">
        <div class="avatar-preview">
          <img :src="avatarPreview || require('@/assets/belog.jpg')" alt="头像预览" class="preview-image">
        </div>
        <div class="avatar-upload-actions">
          <el-upload
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
            :http-request="handleAvatarUpload"
          >
            <el-button type="primary">选择图片</el-button>
          </el-upload>
          <el-button type="success" @click="saveAvatar" :disabled="!avatarPreview">保存</el-button>
        </div>
      </div>
    </el-dialog>

    <main class="main-wrapper" :class="{ 'no-header': isLoginPage }">
      <router-view />
    </main>

    <el-footer class="global-footer" v-if="!isLoginPage">
      <p>© 2025 鸿雁智游 版权所有 | 北京邮电大学 计算机学院（国家示范性软件学院）</p>
    </el-footer>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import LoginDialog from '@/components/LoginDialog.vue'
import { updateAvatar } from '@/api/user' // 导入updateAvatar API
import {
  Star,
  Location,
  Position,
  Ticket,
  Notebook,
  KnifeFork,
  MagicStick
} from '@element-plus/icons-vue'
const backendBaseUrl = 'http://localhost:5000'; // 后端地址
import defaultAvatar from '@/assets/belog.jpg';
const route = useRoute()
const router = useRouter()
const store = useStore()
const loginDialogRef = ref(null)
const avatarDialogVisible = ref(false)
const avatarPreview = ref('')
const activeMenu = ref('') // 当前激活的菜单项

// 根据当前路由设置激活的菜单项
const updateActiveMenu = () => {
  const path = route.path
  if (path.includes('/recommend')) {
    activeMenu.value = 'recommend'
  } else if (path.includes('/route')) {
    activeMenu.value = 'route'
  } else if (path.includes('/search')) {
    activeMenu.value = 'search'
  } else if (path.includes('/flight')) {
    activeMenu.value = 'flight'
  } else if (path.includes('/food')) {
    activeMenu.value = 'food'
  } else if (path.includes('/diary')) {
    activeMenu.value = 'diary'
  } else if (path.includes('/ai-generator')) {
    activeMenu.value = 'ai-generator'
  } else if (path === '/home') {
    activeMenu.value = ''
  }
}

// 监听路由变化，更新激活的菜单项
import { watch, onMounted, onUnmounted } from 'vue'
watch(() => route.path, updateActiveMenu, { immediate: true })
onMounted(updateActiveMenu)

const goToHome = () => {
  // 直接跳转到首页，不做任何检查
  // 因为首页的路由守卫会处理权限问题
  router.push('/home')
}

// 处理菜单选择
const handleMenuSelect = (key) => {
  switch(key) {
    case 'recommend':
      router.push('/recommend')
      break
    case 'route':
      router.push('/route')
      break
    case 'search':
      router.push('/search')
      break
    case 'flight':
      router.push('/flight')
      break
    case 'food':
      router.push('/food')
      break
    case 'diary':
      router.push('/diary')
      break
    case 'ai-generator':
      router.push('/ai-generator')
      break
  }
}
// 使用Vuex store获取用户信息
// const userInfo = computed(() => {
//   // 优先使用Vuex中的用户信息
//   if (store.getters.isAuthenticated && store.getters.currentUser) {
//     return {
//       username: store.getters.currentUser.username,
//       email: store.getters.currentUser.email,
//       avatar: store.getters.currentUser.avatar
//     }
//   }

  // 兼容旧版本：从localStorage获取
//   const currentUser = localStorage.getItem('currentUser')
//   if (currentUser) {
//     try {
//       const userData = JSON.parse(currentUser)

//       // 如果Vuex中没有用户信息，但localStorage有，则同步到Vuex
//       if (!store.getters.isAuthenticated) {
//         const token = localStorage.getItem('token')
//         if (token) {
//           store.dispatch('login', { user: userData, token })
//         }
//       }

//       return {
//         username: userData.username,
//         email: userData.email,
//         avatar: avatarUrl.value // 统一用 avatarUrl
//       }
//     } catch (e) {
//       console.error('解析用户信息出错:', e)
//     }
//   }

//   return {
//     username: 'guest',
//     email: '',
//     avatar: defaultAvatar
//   }
// })
function getFullAvatarUrl(avatarPath) {
  if (!avatarPath) return defaultAvatar;
  if (typeof avatarPath === 'string') {
    if (avatarPath.startsWith('/uploads/')) {
      return backendBaseUrl + avatarPath;
    }
    if (
      avatarPath.startsWith('http://') ||
      avatarPath.startsWith('https://') ||
      avatarPath.startsWith('data:image')
    ) {
      return avatarPath;
    }
  }
  return defaultAvatar;
}
// 头像URL计算属性 - 优先使用Vuex，然后是localStorage
const avatarUrl = computed(() => {
  // 强制依赖userInfoTrigger，确保在更新时重新计算
  //const _ = userInfoTrigger.value;

  // 1. 优先从Vuex获取
  if (store.getters.isAuthenticated && store.getters.currentUser) {
    const avatar = store.getters.currentUser.avatar;
    console.log('App.vue - 从Vuex获取头像:', avatar);
    // 如果用户没有头像或头像为默认值，返回默认头像
    if (!avatar || avatar === '' || avatar === null || avatar === undefined || avatar === 'default_avatar.jpg') {
      console.log('App.vue - Vuex中用户无头像或为默认头像，使用默认头像');
      return defaultAvatar;
    }
    return getFullAvatarUrl(avatar);
  }

  // 2. 如果Vuex中没有，从localStorage获取
  try {
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
      const userData = JSON.parse(currentUser);
      console.log('App.vue - 从localStorage获取头像:', userData.avatar);
      // 如果用户没有头像或头像为默认值，返回默认头像
      if (!userData.avatar || userData.avatar === '' || userData.avatar === null || userData.avatar === undefined || userData.avatar === 'default_avatar.jpg') {
        console.log('App.vue - localStorage中用户无头像或为默认头像，使用默认头像');
        return defaultAvatar;
      }
      return getFullAvatarUrl(userData.avatar);
    }
  } catch (e) {
    console.error('App.vue - 解析localStorage中的用户信息失败:', e);
  }

  // 3. 默认头像
  console.log('App.vue - 使用默认头像');
  return defaultAvatar;
});


// 判断当前是否为登录页面
const isLoginPage = computed(() => {
  return route.name === 'LoginEntry'
})

// 用户信息更新触发器
const userInfoTrigger = ref(0)

// 处理用户信息更新，包括从其他组件触发的头像更新
function updateUserInfo(event) {
  console.log('App.vue - 收到用户信息更新事件:', event);

  // 如果是自定义事件并且包含头像信息
  if (event && event.detail && event.detail.avatar) {
    console.log('App.vue - 收到头像更新:', event.detail.avatar);

    // 如果Vuex中有用户信息，更新Vuex
    if (store.getters.isAuthenticated && store.getters.currentUser) {
      const user = { ...store.getters.currentUser, avatar: event.detail.avatar };
      store.commit('SET_USER', user);
      console.log('App.vue - 从事件更新了Vuex中的头像');
    }
  }

  // 触发响应式更新
  userInfoTrigger.value++;
  console.log('App.vue - 触发响应式更新, userInfoTrigger =', userInfoTrigger.value);
}

const avatarText = computed(() => {
  // 优先使用Vuex中的登录状态
  if (store.getters.isAuthenticated) {
    return '个人中心'
  }

  // 兼容旧版本：从localStorage获取
  const currentUser = localStorage.getItem('currentUser')
  return currentUser ? '个人中心' : '注册/登录'
})

const handleAvatarClick = () => {
  // 优先使用Vuex中的登录状态
  if (!store.getters.isAuthenticated) {
    // 检查localStorage兼容旧版本
    const currentUser = localStorage.getItem('currentUser')
    if (!currentUser) {
      loginDialogRef.value?.show()
    } else {
      // 如果localStorage有但Vuex没有，同步到Vuex
      try {
        const userData = JSON.parse(currentUser)
        const token = localStorage.getItem('token')
        if (token) {
          store.dispatch('login', { user: userData, token })
        }
        avatarDialogVisible.value = true
      } catch (e) {
        console.error('解析用户信息出错:', e)
        loginDialogRef.value?.show()
      }
    }
  } else {
    //已登录用户点击显示头像上传对话框
    avatarDialogVisible.value = true
  }
}

// 处理文字点击
const handleTextClick = () => {
  // 优先使用Vuex中的登录状态
  if (!store.getters.isAuthenticated) {
    // 检查localStorage兼容旧版本
    const currentUser = localStorage.getItem('currentUser')
    if (!currentUser) {
      // 未登录用户点击显示登录对话框
      loginDialogRef.value?.show()
    } else {
      // 如果localStorage有但Vuex没有，同步到Vuex
      try {
        const userData = JSON.parse(currentUser)
        const token = localStorage.getItem('token')
        if (token) {
          store.dispatch('login', { user: userData, token })
        }
        router.push('/user-center')
      } catch (e) {
        console.error('解析用户信息出错:', e)
        loginDialogRef.value?.show()
      }
    }
  } else {
    // 已登录用户点击跳转到个人中心页面
    router.push('/user-center')
  }
}

// 头像上传前的验证
const beforeAvatarUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传头像图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

// 处理头像上传
const handleAvatarUpload = (options) => {
  const file = options.file
  // 将文件转换为 base64
  const reader = new FileReader()
  reader.readAsDataURL(file)
  reader.onload = () => {
    avatarPreview.value = reader.result
  }
}
// 添加事件监听
const isScrolled = ref(false)

// 监听滚动事件，为导航栏添加滚动效果
const handleScroll = () => {
  isScrolled.value = window.scrollY > 10
}

onMounted(() => {
  window.addEventListener('user-avatar-updated', updateUserInfo)
  window.addEventListener('scroll', handleScroll)
})
onUnmounted(() => {
  window.removeEventListener('user-avatar-updated', updateUserInfo)
  window.removeEventListener('scroll', handleScroll)
})
// 保存头像
const saveAvatar = async () => {
  if (avatarPreview.value) {
    try {
      // 1. 将Base64转换为Blob/File对象
      const base64Response = await fetch(avatarPreview.value);
      const blob = await base64Response.blob();
      const file = new File([blob], "avatar.jpg", { type: blob.type });
      let userId = null;
      if (store.getters.isAuthenticated && store.getters.currentUser) {
        userId = store.getters.currentUser.user_id || store.getters.currentUser.id;
      }
      if (!userId) {
        const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
        userId = currentUser.user_id || currentUser.id;
      }
      if (!userId) {
        ElMessage.error('无法获取用户ID，请重新登录');
        return;
      }

      const formData = new FormData();
      formData.append('user_id', userId);
      formData.append('avatar', file);

      console.log("App.vue - 调用updateAvatar API...");
      const response = await updateAvatar(formData);
      console.log("App.vue - updateAvatar API响应:", response);

      if (response && response.code === 0 && response.data && response.data.avatar_url) {
        const newAvatarPath = response.data.avatar_url;

        // 4. 更新Vuex store
        if (store.getters.isAuthenticated) {
          const user = { ...store.getters.currentUser, avatar: newAvatarPath };
          store.commit('SET_USER', user);
          console.log('App.vue - 已更新Vuex store中的头像');
        }

        // 5. 更新localStorage (兼容旧版本)
        const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
        if (currentUser) {
          currentUser.avatar = newAvatarPath;
          localStorage.setItem('currentUser', JSON.stringify(currentUser));
          console.log('App.vue - 已更新localStorage中的头像');
        }

        window.dispatchEvent(new CustomEvent('user-avatar-updated', {
          detail: { avatar: newAvatarPath }
        }));
        console.log('App.vue - 已触发user-avatar-updated事件');

        ElMessage.success('头像更新成功');
        avatarDialogVisible.value = false;
      } else {
        console.error('App.vue - 头像更新失败，响应:', response);
        ElMessage.error(response?.message || '头像更新失败：服务器响应异常');
      }
    } catch (error) {
      console.error('App.vue - 保存头像出错:', error);
      if (error.response) {
        const backendMessage = error.response.data?.message || error.response.data?.error || '服务器响应错误';
        ElMessage.error(`头像更新失败: ${backendMessage}`);
      } else {
        ElMessage.error('保存头像失败: ' + (error.message || '未知错误'));
      }
    }
  }
}
</script>

<style scoped>
.header-title:hover {
  color: #409EFF;
}

.header-logo {
  cursor: pointer;
  transition: transform 0.3s ease;
}

.header-logo:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

.global-header {
  padding: 0 1rem 0 0.5rem; /* 左侧减少padding，右侧保持 */
  background: linear-gradient(135deg, #3971db 0%, #032d56 100%);
  box-shadow: 0 4px 15px rgba(0,0,0,0.15);
  backdrop-filter: blur(8px);
  position: fixed; /* 改为固定定位，使导航栏始终固定在页面顶部 */
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000; /* 增大 z-index 确保导航栏始终在最上层 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px !important; /* 固定高度，覆盖 el-header 的默认高度 */
  overflow: visible; /* 允许内容溢出，确保图标可以显示完整 */
  transition: all 0.3s ease; /* 添加过渡效果 */
}

/* 滚动时的导航栏样式 */
.global-header.scrolled {
  background: linear-gradient(135deg, rgba(57, 113, 219, 0.95) 0%, rgba(3, 45, 86, 0.95) 100%);
  box-shadow: 0 5px 20px rgba(0,0,0,0.2);
  backdrop-filter: blur(15px);
  height: 55px !important; /* 滚动时略微降低高度 */
}

.global-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('@/assets/forbidden_city.jpg');
  background-size: cover;
  background-position: center;
  opacity: 0.05;
  z-index: -1;
}

.header-container {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 0.8rem; /* 进一步减少间距 */
  max-width: 1500px; /* 增加最大宽度 */
  margin: 0;
  padding-left: 0.3rem; /* 进一步减少左侧间距 */
}

.header-title {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* 减少间距，保持整体宽度 */
  flex-grow: 1;
  height: 60px; /* 固定高度，确保导航栏高度不变 */
}

.header-icon {
  width: 2.4rem;
  height: 2.4rem;
  color: var(--el-color-primary);
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
  transition: transform 0.3s ease;
}

.header-text {
  font-family: 'Segoe UI Variable', 'PingFang SC', sans-serif;
  font-size: clamp(1.5rem, 4vw, 2rem);
  font-weight: 600;
  line-height: 1.2;
  white-space: nowrap;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 添加文字阴影 */
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.header-title:hover .header-icon {
  transform: translateY(-2px);
}

.header-actions {
  display: flex;
  gap: 1.5rem;
}

.dark-mode .header-text {
  color: rgba(255,255,255,0.85);
}

.main-wrapper {
  min-height: calc(100vh - 160px);
  padding: 2rem;
  margin-top: 60px; /* 为固定导航栏留出空间 */
}

.main-wrapper.no-header {
  min-height: 100vh;
  padding: 0;
  margin-top: 0; /* 登录页面没有导航栏，不需要留出空间 */
}

.header-logo {
  width: 4.2rem;        /* 控制图片宽度 - 增大尺寸 */
  height: 4.2rem;       /* 保持宽高比 - 增大尺寸 */
  margin-right: 0.5rem; /* 减少与文字间距，保持整体宽度 */
  filter: brightness(1); /* 调整亮度 - 提高亮度使图标更清晰 */
  object-fit: contain;  /* 确保图片完全适应容器 */
  max-height: 60px;     /* 限制最大高度，避免影响导航栏高度 */
  transition: all 0.3s ease;
}

/* 悬停效果 */
.header-title:hover .header-logo {
  filter: brightness(1.2);
  transform: scale(1.05);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .header-container {
    gap: 1rem;
    padding: 0 1rem;
  }

  .header-text {
    font-size: 1.2rem;
  }
}

.global-footer {
  background: #f5f7fa;
  padding: 1rem;
  text-align: center; /* 新增居中属性 */
}

.global-footer p {
  margin: 0; /* 去除默认外边距 */
  font-size: 0.9rem;
  color: #343434;
  line-height: 1.5;
}
.right-image {
  position: relative;
  display: block;
  width: 42px;
  height: 42px;
  border-radius: 50%;  /* 设置为50%得到正圆形 */
  object-fit: cover;   /* 保持图片比例 */
  border: 2px solid rgba(255, 255, 255, 0.8); /* 半透明白色边框 */
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2); /* 增强阴影效果 */
  cursor: pointer; /* 添加指针样式 */
  transition: all 0.3s ease; /* 添加过渡效果 */
  margin-right: 10px; /* 右侧间距 */
}
.right-image:hover {
  transform: scale(1.1); /* 悬停时放大效果 */
}
.header-right {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 5px 15px;
  border-radius: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.header-right:hover {
  background-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px; /* 图片和文字的间距 */
  justify-content: center;
  position: relative;
}
.avatar-text {
  font-size: 14px; /* 增大字号 */
  color: #ffffff; /* 白色文字 */
  white-space: nowrap; /* 防止文字换行 */
  text-align: center;
  cursor: pointer; /* 添加指针样式 */
  transition: all 0.3s ease; /* 添加过渡效果 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* 添加文字阴影 */
  font-weight: 500; /* 略微加粗 */
  margin-right: 10px; /* 右侧间距 */
}
.avatar-text:hover {
  color: #e6f7ff; /* 悬停时颜色变化 */
}

/* 头像上传对话框样式 */
.avatar-upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.avatar-preview {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #dcdfe6;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-upload-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

/* 自定义菜单样式 */
.custom-menu {
  border-bottom: none !important;
  background-color: transparent !important;
  margin-left: auto;
  margin-right: 20px;
}

.menu-item {
  height: 60px !important;
  line-height: 60px !important;
  margin: 0 5px !important;
  font-size: 1rem !important;
  color: rgba(255, 255, 255, 0.85) !important;
  border-bottom: 3px solid transparent !important;
  transition: all 0.3s ease !important;
}

.menu-item:hover, .menu-item.is-active {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
  border-bottom: 3px solid #409EFF !important;
}

.menu-item.is-active {
  font-weight: bold !important;
}

.menu-item .el-icon {
  margin-right: 5px;
  font-size: 1.1rem;
  vertical-align: middle;
}

/* AI生成菜单项特殊样式 */
.ai-menu-item {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%) !important;
  border-radius: 8px !important;
  margin: 0 8px !important;
  font-weight: 600 !important;
}

.ai-menu-item:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3) !important;
}

.ai-menu-item.is-active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.4) 0%, rgba(118, 75, 162, 0.4) 100%) !important;
  border-bottom: 3px solid #ffd700 !important;
}
</style>
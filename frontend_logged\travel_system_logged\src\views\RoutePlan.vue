<template>
  <div class="route-plan-main">
    <!-- 导航标签页 -->
    <div class="navigation-tabs">
      <div class="tabs-container">
        <div class="tab-header">
          <h1>路线规划</h1>
          <p class="tab-subtitle">选择您要导航的区域</p>
        </div>
        <div class="tabs">
          <router-link
            to="/route/bupt"
            class="tab-item"
            :class="{ active: $route.name === 'BUPTNavigation' }"
          >
            <div class="tab-icon">🏫</div>
            <div class="tab-content">
              <div class="tab-title">北京邮电大学导航</div>
              <div class="tab-description">校园内路线规划与导航</div>
            </div>
          </router-link>

          <router-link
            to="/route/chaoyang-park"
            class="tab-item"
            :class="{ active: $route.name === 'ChaoyangParkNavigation' }"
          >
            <div class="tab-icon">🌳</div>
            <div class="tab-content">
              <div class="tab-title">北京朝阳公园导航</div>
              <div class="tab-description">公园内路线规划与导航</div>
            </div>
          </router-link>
        </div>
      </div>
    </div>

    <!-- 子页面内容 -->
    <div class="route-content">
      <router-view />
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
  console.log('路线规划主页面已加载')
})
</script>

<style scoped>
.route-plan-main {
  height: 130vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.navigation-tabs {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tabs-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.tab-header {
  text-align: center;
  margin-bottom: 30px;
}

.tab-header h1 {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.tab-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  margin: 0;
  font-weight: 300;
}

.tabs {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.tab-item {
  display: flex;
  align-items: center;
  padding: 20px 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  text-decoration: none;
  color: white;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  min-width: 280px;
}

.tab-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.tab-item.active {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.tab-icon {
  font-size: 2.5rem;
  margin-right: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.tab-content {
  flex: 1;
}

.tab-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.tab-description {
  font-size: 0.95rem;
  opacity: 0.9;
  font-weight: 300;
}

.route-content {
  flex: 1;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tabs {
    flex-direction: column;
    align-items: center;
  }

  .tab-item {
    min-width: 250px;
    max-width: 350px;
  }

  .tab-header h1 {
    font-size: 2rem;
  }

  .tab-subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .navigation-tabs {
    padding: 15px 0;
  }

  .tabs-container {
    padding: 0 15px;
  }

  .tab-item {
    padding: 15px 20px;
    min-width: 200px;
  }

  .tab-icon {
    font-size: 2rem;
    margin-right: 15px;
  }

  .tab-title {
    font-size: 1.1rem;
  }

  .tab-description {
    font-size: 0.9rem;
  }
}
</style>